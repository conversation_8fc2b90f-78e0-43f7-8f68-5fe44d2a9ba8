import { BoardStatusEnum, type BoardWithCountVO } from '@repo/common/types/board';
import { FavoriteEntityTypeEnum } from '@repo/common/types/favorite';
import { Button } from '@repo/ui/components/ui/button';
import { DialogTrigger } from '@repo/ui/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { toast } from '@repo/ui/components/ui/sonner';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import * as boardIcons from '@youmindinc/youicon';
import { useUpdateEffect } from 'ahooks';
import { useAtomValue, useSetAtom } from 'jotai';
import {
  Archive,
  Ellipsis,
  SquarePen,
  Star,
  StarOff,
  Trash,
  ArchiveRestore as Unarchive,
} from 'lucide-react';
import { useState } from 'react';
import BoardCreator, { PALETTE } from '@/components/board/board-creator';
import { DeleteDoubleCheckDialog } from '@/components/delete-double-check-dialog';
import { archiveBoardAtom, boardsAtom, unarchiveBoardAtom } from '@/hooks/useBoards';
import { useFavorite } from '@/hooks/useFavorite';
import { openDropdownMenuCountAtom } from '@/hooks/useSidebar';
import { useTranslation } from '@/hooks/useTranslation';
import { cn } from '@/utils/utils';

interface BoardModifyButtonProps {
  board: BoardWithCountVO;
  dialogAlign?: 'start' | 'end';
  itemsCount: number;
  buttonWidth?: number;
  className?: string;
  children?: React.ReactNode;
  onBoardEditFinish?: (data: Partial<BoardWithCountVO>) => void;
  onBoardDelete: () => void;
  onBoardPinChange?: (pin: Date | undefined) => void;
  onArchiveChange?: (status: BoardStatusEnum) => void;
  menuOpen?: boolean;
  onMenuOpenChange?: (open: boolean) => void;
  canArchive?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  canFavorite?: boolean;
}

export default function BoardModifyButton({
  board,
  dialogAlign = 'end',
  itemsCount,
  className,
  buttonWidth = 24,
  onBoardEditFinish,
  onBoardDelete,
  onArchiveChange,
  children,
  menuOpen: externalMenuOpen,
  onMenuOpenChange: externalOnMenuOpenChange,
  canArchive = true,
  canEdit = true,
  canDelete = true,
  canFavorite = true,
}: BoardModifyButtonProps) {
  const archiveBoard = useSetAtom(archiveBoardAtom);
  const unarchiveBoard = useSetAtom(unarchiveBoardAtom);
  const boards = useAtomValue(boardsAtom);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { getEntityFavorite, toggleFavorite, setFavorites } = useFavorite();

  // 内部菜单状态，当没有外部控制时使用
  const [internalMenuOpen, setInternalMenuOpen] = useState(false);
  const setOpenDropdownMenuCount = useSetAtom(openDropdownMenuCountAtom);

  const { t } = useTranslation('Library.Board');

  const { trackButtonClick } = useTrackActions();

  // 使用外部传入的状态，如果没有则使用内部状态
  const menuOpen = externalMenuOpen !== undefined ? externalMenuOpen : internalMenuOpen;
  const setMenuOpen = externalOnMenuOpenChange || setInternalMenuOpen;

  // 监听菜单打开/关闭状态，更新全局计数器
  useUpdateEffect(() => {
    if (menuOpen) {
      setOpenDropdownMenuCount((count) => count + 1);
    } else {
      setOpenDropdownMenuCount((count) => Math.max(0, count - 1));
    }
  }, [menuOpen]);

  const handleDeleteBoard = async () => {
    if (boards.length === 1) {
      toast(t('deleteLimitTips'));
      return;
    }
    // 删除 favorite
    setFavorites((prev) => prev.filter((favorite) => favorite.entity.id !== board.id));
    // 删除 board 里所有 item 的 favorite
    setFavorites((prev) =>
      prev.filter((favorite) => {
        const favItemBoardId =
          // @ts-expect-error board_id is optional
          favorite.entity.board_id || favorite.entity.board_ids?.[0];
        return favItemBoardId !== board.id;
      }),
    );

    onBoardDelete();
  };

  const handleBoardChanged = (data: Partial<BoardWithCountVO>) => {
    onBoardEditFinish?.(data);
  };

  const handleArchiveChange = async () => {
    try {
      if (board.status === BoardStatusEnum.IN_PROGRESS) {
        await archiveBoard(board.id);
        onArchiveChange?.(BoardStatusEnum.OTHER);
      } else {
        await unarchiveBoard(board.id);
        onArchiveChange?.(BoardStatusEnum.IN_PROGRESS);
      }
    } catch (e) {
      toast((e as Error).message);
    }
  };

  // @ts-expect-error icon name is dynamic
  const Icon = boardIcons[board?.icon?.name] || boardIcons.Planet;
  const color = `hsl(var(${board?.icon?.color || PALETTE[0]}))`;

  const isFavorite = getEntityFavorite(board.id);

  // 算一下有多少个可以显示的项
  const showingCount = [canEdit, canFavorite, canArchive, canDelete].filter(Boolean).length;

  return (
    <BoardCreator board={board} variant="edit" syncToBoardList onBoardChanged={handleBoardChanged}>
      <DropdownMenu open={menuOpen} onOpenChange={setMenuOpen}>
        <DropdownMenuTrigger asChild>
          {children ? (
            children
          ) : (
            <Button
              size="icon"
              variant="ghost"
              className={cn('rounded-full text-foreground', className)}
              style={{
                width: buttonWidth,
                height: buttonWidth,
              }}
            >
              <Ellipsis size={16} />
            </Button>
          )}
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-[190px] rounded-2xl border-none p-3" align={dialogAlign}>
          <DropdownMenuGroup>
            {canEdit && (
              <DropdownMenuItem className="cursor-pointer" asChild>
                <DialogTrigger asChild>
                  <div
                    className="flex items-center w-full h-full"
                    onClick={(_e) => {
                      // 上报埋点
                      trackButtonClick('board_modify_menu_edit_click');
                    }}
                  >
                    <SquarePen size={16} />
                    {t('edit')}
                  </div>
                </DialogTrigger>
              </DropdownMenuItem>
            )}

            {canFavorite && (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => {
                  toggleFavorite({
                    entity_id: board.id,
                    entity_type: FavoriteEntityTypeEnum.BOARD,
                    entity: board,
                  });
                  // 上报埋点
                  trackButtonClick('board_modify_menu_favorite_click', {
                    target_state: !isFavorite,
                  });
                }}
              >
                {isFavorite ? <StarOff size={16} /> : <Star size={16} />}
                {isFavorite ? 'Unfavorite' : 'Favorite'}
              </DropdownMenuItem>
            )}

            {canArchive && (
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => {
                  handleArchiveChange();
                  // 上报埋点
                  trackButtonClick('board_modify_menu_archive_click', {
                    target_state: board.status === BoardStatusEnum.IN_PROGRESS,
                  });
                }}
              >
                <div className="flex items-center gap-2">
                  {board.status === BoardStatusEnum.IN_PROGRESS ? (
                    <>
                      <Archive size={16} />
                      {t('archive')}
                    </>
                  ) : (
                    <>
                      <Unarchive size={16} />
                      {t('unarchive')}
                    </>
                  )}
                </div>
              </DropdownMenuItem>
            )}

            {/* <DropdownMenuItem
              className="cursor-pointer"
              onClick={() => setFocusMode(!focusMode)}
            >
              <div className="flex items-center">
                <ScanEye size={16} />
                <span className="ml-2">{t("focusMode")}</span>
                <span className="flex items-center justify-center w-4 h-4 ml-2 border rounded-sm shadow-sm border-border text-foreground">
                  <ArrowBigUp size={13} className="opacity-75" />
                </span>
                <span className="ml-1 flex h-4 w-4 items-center justify-center rounded-sm border border-border text-[10px] text-foreground shadow-sm">
                  F
                </span>
              </div>
            </DropdownMenuItem> */}
          </DropdownMenuGroup>

          {/* 只有在有可删除权限时才显示分隔线和删除项 */}
          {showingCount > 3 && <DropdownMenuSeparator />}

          {canDelete && (
            <DropdownMenuItem
              className="cursor-pointer"
              onClick={() => {
                setDeleteDialogOpen(true);
                // 上报埋点
                trackButtonClick('board_modify_menu_delete_click');
              }}
            >
              <Trash size={16} />
              {t('delete')}...
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <DeleteDoubleCheckDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        header={<Icon size={32} style={{ color }} />}
        title={`Delete " ${board?.name} " board?`}
        deleteButtonText="Delete"
        description={`This board and all of its ${itemsCount} ${
          itemsCount <= 1 ? 'item' : 'items'
        } will be deleted and cannot be recovered.`}
        onConfirm={handleDeleteBoard}
      />
    </BoardCreator>
  );
}
