'use client';

import { Button } from '@repo/ui/components/ui/button';
import { X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSnipSaving } from '@/hooks/useSnipSaving';
import { SavingPanel } from './SavingPanel';

type SnipSavingPopupProps = {};

export const SnipSavingPopup: React.FC<SnipSavingPopupProps> = () => {
  const { savingSnips, removeAllSavingSnips } = useSnipSaving();
  const [isMinimized, setIsMinimized] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const allSuccess = useMemo(() => {
    return (
      savingSnips.length > 0 &&
      savingSnips
        .filter((snip) => snip.status !== 'removed' && snip.status !== 'hidden')
        .every((snip) => snip.status === 'success')
    );
  }, [savingSnips]);

  const allDone = useMemo(() => {
    return (
      savingSnips.length > 0 &&
      savingSnips
        .filter((snip) => snip.status !== 'removed' && snip.status !== 'hidden')
        .every((snip) => snip.status === 'success' || snip.status === 'failed')
    );
  }, [savingSnips]);

  // 监听 savingSnips 变化，处理自动清除逻辑
  useEffect(() => {
    // 如果全部成功，启动倒计时
    if (allSuccess) {
      // 清除之前可能存在的定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      // 设置新的 5 秒倒计时
      timerRef.current = setTimeout(() => {
        removeAllSavingSnips();
      }, 5000);
    } else {
      // 如果不是全部成功，取消倒计时
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    }

    // 组件卸载时清除定时器
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [removeAllSavingSnips, allSuccess]);

  // console.log("savingSnips", savingSnips);

  // 切换最小化状态
  const toggleMinimize = useCallback(() => {
    if (isAnimating) return; // 防止动画过程中重复点击

    setIsAnimating(true);
    setIsMinimized((prev) => !prev);

    // 清除之前的定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 动画完成后重置状态
    timerRef.current = setTimeout(() => {
      setIsAnimating(false);
    }, 400); // 与CSS中的transition时间匹配
  }, [isAnimating]);

  if (
    savingSnips.filter((item) => item.status !== 'removed' && item.status !== 'hidden').length === 0
  )
    return null;

  const savingItemCount = savingSnips.filter((item) => item.status === 'saving').length;
  const successItemCount = savingSnips.filter((item) => item.status === 'success').length;
  const failedItemCount = savingSnips.filter((item) => item.status === 'failed').length;

  // 计算总数（不包括已移除的）
  const totalItemCount = savingSnips.filter(
    (item) => item.status !== 'removed' && item.status !== 'hidden',
  ).length;

  // 检查是否所有项目都已完成（成功或失败）
  const allItemsCompleted = savingItemCount === 0 && totalItemCount > 0;

  return (
    <div
      className="pointer-events-none fixed bottom-0 left-1/2 z-50 -translate-x-1/2"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {allDone && isHovered ? (
        <Button
          variant="icon"
          size="xs"
          className="pointer-events-auto absolute -top-3 right-[2px] z-10 flex h-5 w-5 items-center justify-center rounded-full border border-divider bg-card text-caption-foreground"
          onClick={() => removeAllSavingSnips()}
        >
          <X size={12} />
        </Button>
      ) : null}
      <SavingPanel
        isVisible={!isMinimized}
        savingSnips={savingSnips}
        savingItemCount={savingItemCount}
        successItemCount={successItemCount}
        failedItemCount={failedItemCount}
        totalItemCount={totalItemCount}
        allItemsCompleted={allItemsCompleted}
        onMinimize={toggleMinimize}
        removeAllSavingSnips={removeAllSavingSnips}
      />
    </div>
  );
};
