'use client';

import { ButtonWithTooltip } from '@repo/ui/components/ui/button';
// import { useSetAtom } from 'jotai';
import { Ban, Check, CircleAlert, Loader2 } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Earth } from '@/components/icon/earth';
// import { useFlattenBoardItemTree } from '@/hooks/useBoardItemTree/useFlattenBoardItemTree';
// import { changePanelDataAtom } from '@/hooks/useBoardState';
import { SavingSnipItem, useSnipSaving } from '@/hooks/useSnipSaving';
import {
  AUDIO_ICON,
  SMALL_PDF_ICON,
  SNIP_TEXT_FILE_ICON,
  VIDEO_ICON,
  WEBSITE_ICON,
} from '@/utils/board/getImageAndTitleFromBoardItem';
import { getTextFileIconByFileName } from '../../snip-content/text-file';

interface SavingItemProps {
  item: SavingSnipItem;
}

// 使用memo包装组件，避免不必要的重渲染
export const SavingItem: React.FC<SavingItemProps> = memo(({ item }) => {
  const [isHovered, setIsHovered] = useState(false);
  const { removeOneSavingSnip } = useSnipSaving();
  // const changePanelData = useSetAtom(changePanelDataAtom);

  // const { flattenedItems } = useFlattenBoardItemTree();
  // const router = useRouter();
  // const pathname = usePathname() || '';

  // 为图片类型文件缓存URL
  const [imageObjectUrl, setImageObjectUrl] = useState<string | null>(null);

  // 标题是否可点击跳转到 snip detail 页
  const snipDetailId = useMemo(() => {
    if (item.disableClick) {
      return null;
    }
    if (item.type === 'snip') {
      if (item.canDelete) {
        return item.entity.id;
      }
      if (item.errorMessage?.includes('exists')) {
        return item.entity?.id || null;
      }
    }
    if (item.type === 'file') {
      if (item.savedSnip?.id) {
        return item.savedSnip.id;
      }
    }
    return null;
  }, [item]);

  // 使用useEffect处理图片URL的创建和清理
  useEffect(() => {
    if (item.type === 'file' && item.entity.type.includes('image')) {
      const url = URL.createObjectURL(item.entity);
      setImageObjectUrl(url);

      // 组件卸载时清理URL对象
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [item]);

  // 使用useCallback缓存事件处理函数
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  const handleDelete = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      if (item.status === 'saving') {
        // saving 的要真删除
        removeOneSavingSnip(item);
      } else {
        // 其他状态的只是移除
        removeOneSavingSnip(item, false);
      }
    },
    [item, removeOneSavingSnip],
  );

  // 使用useMemo缓存状态渲染
  const statusElement = useMemo(() => {
    if (item.status === 'saving') {
      return <Loader2 size={13} className="animate-spin" />;
    }
    if (item.status === 'success') {
      return <Check size={13} className="text-[#28CD41]" />;
    }
    return <CircleAlert size={13} className="text-[#EA4E43]" />;
  }, [item.status]);

  // 使用useMemo缓存删除按钮
  const actionButton = useMemo(() => {
    const isSaving = item.status === 'saving';
    if (isSaving) {
      return (
        <ButtonWithTooltip
          tooltip={isSaving ? 'Cancel' : 'Hide'}
          variant="icon"
          className="flex h-5 w-5 items-center justify-center rounded-full bg-muted transition-colors"
          size="xs"
          onClick={handleDelete}
        >
          <Ban size={12} />
        </ButtonWithTooltip>
      );
    }
    return null;
  }, [handleDelete, item.status]);

  let canDelete = item.canDelete;
  if (item.status !== 'saving') {
    canDelete = false;
  }

  // 使用CSS控制显示/隐藏，而不是条件渲染
  const renderContent = () => {
    // 提取共同的变量
    let title = '';
    let imageUrl: string | null = null;

    // 根据类型获取不同的标题和图片
    if (item.type === 'snip') {
      const snip = item.entity;
      title =
        // @ts-expect-error 忽略一下 - snip 实体的类型定义可能不完整
        snip.title || snip.webpage?.title || snip.webpage?.url || 'Unknown';
      // @ts-expect-error 忽略一下 - snip 实体的类型定义可能不完整
      imageUrl = snip.webpage?.site?.favicon_url;
    } else {
      // file
      const file = item.entity;
      title = file.name;
      imageUrl = getTextFileIconByFileName(file?.name, false) || SNIP_TEXT_FILE_ICON;
      if (file.type.includes('image') && imageObjectUrl) {
        imageUrl = imageObjectUrl;
      }
      if (file.type.includes('pdf')) {
        imageUrl = SMALL_PDF_ICON;
      }
      if (file.type.includes('audio')) {
        imageUrl = AUDIO_ICON;
      }
      if (file.type.includes('video')) {
        imageUrl = VIDEO_ICON;
      }
    }

    // 渲染统一的UI结构
    return (
      <div
        className="flex items-center justify-between gap-2 py-2 text-sm font-normal"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div
          className="flex min-w-0 items-center gap-2 overflow-hidden"
          style={{
            opacity: item.status === 'saving' ? 0.6 : 1,
          }}
        >
          {item.type === 'snip' && !imageUrl ? (
            <Earth size={16} className="flex-shrink-0" />
          ) : (
            <img
              src={imageUrl || WEBSITE_ICON}
              alt={title}
              className="h-4 w-4 min-w-[16px] flex-shrink-0 rounded-[3px] object-contain"
            />
          )}

          {snipDetailId ? (
            <div className="truncate text-left">{title}</div>
          ) : (
            <span className="truncate">{title}</span>
          )}
        </div>
        <div className="relative flex h-[20px] min-w-[20px] flex-shrink-0 items-center justify-end gap-2">
          {item.status === 'failed' &&
            item.errorMessage &&
            (snipDetailId ? (
              <div className="text-left text-xs text-[#EA4E43]">
                {item.errorMessage?.replace(/\./g, '') || 'failed'}
              </div>
            ) : (
              <span className="text-xs text-[#EA4E43]">
                {item.errorMessage?.replace(/\./g, '') || 'failed'}
              </span>
            ))}
          <div className="flex w-5 flex-shrink-0 items-center justify-center">
            {isHovered && canDelete ? actionButton : statusElement}
          </div>
        </div>
      </div>
    );
  };

  return renderContent();
});

// 添加显示名称以便调试
SavingItem.displayName = 'SavingItem';
