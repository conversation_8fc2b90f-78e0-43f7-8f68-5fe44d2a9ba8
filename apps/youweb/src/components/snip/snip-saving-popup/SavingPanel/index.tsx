import { Button } from '@repo/ui/components/ui/button';
import { useToast } from '@repo/ui/components/ui/use-toast';
import { Check, ChevronDown, ChevronUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import type { SavingSnipItem } from '@/hooks/useSnipSaving';
import { cn } from '@/utils/utils';
import { LoadingBook } from '../../snip-thumbnail-unknown';
import styles from './index.module.css';
import { SavingItem } from './SavingItem';

interface PanelProps {
  isVisible: boolean;
  savingSnips: SavingSnipItem[];
  savingItemCount: number;
  successItemCount: number;
  failedItemCount: number;
  totalItemCount: number;
  allItemsCompleted: boolean;
  onMinimize: () => void;
  removeAllSavingSnips: () => void;
}

// 面板组件
export const SavingPanel: React.FC<PanelProps> = ({
  isVisible,
  savingSnips,
  savingItemCount,
  successItemCount,
  failedItemCount,
  totalItemCount,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const { dismiss } = useToast();

  // 唯我独尊
  useEffect(() => {
    dismiss();
  }, []);

  // const allSuccess =
  //   savingSnips.length > 0 &&
  //   savingSnips
  //     .filter((snip) => snip.status !== "removed" && snip.status !== "hidden")
  //     .every((snip) => snip.status === "success");

  // 生成状态文本
  const getStatusText = () => {
    // 当有正在保存的项目时，显示进度信息
    if (savingItemCount > 0) {
      return (
        <>
          Adding {savingItemCount} new items...
          <span className="ml-2 text-caption-foreground">
            ({totalItemCount - savingItemCount}/{totalItemCount})
          </span>
        </>
      );
    }

    // 当没有正在保存的项目时，显示固定格式的结果
    return `${successItemCount} added, ${failedItemCount} failed`;
  };

  return (
    <div className={cn(styles.panelContainer, 'text-foreground')}>
      <div
        className={cn(
          `${styles.panel} ${isVisible ? styles.panelVisible : styles.panelHidden}`,
          'bg-card',
        )}
        style={{ pointerEvents: 'auto' }}
      >
        <div className="mb-2 flex w-full items-center justify-between text-base font-medium">
          <div className="flex items-center text-sm font-medium">
            <div className="mr-2 h-6 w-6 text-caption-foreground">
              {savingItemCount > 0 ? (
                <LoadingBook className="relative -left-2 bottom-5 scale-[.15]" />
              ) : (
                <Check size={22} />
              )}
            </div>
            <div className={savingItemCount > 0 ? 'loading-shimmer' : ''}>{getStatusText()}</div>
          </div>

          <div className="flex items-center justify-center gap-2 pr-4 text-muted-foreground">
            {/* {savingItemCount > 0 ? (
              <DeleteDoubleCheckDialog
                header={""}
                title={`Delete all saving items?`}
                deleteButtonText="Delete"
                description={`All saving ${savingItemCount} ${
                  savingItemCount <= 1 ? "item" : "items"
                } will be cancelled and cannot be recovered.`}
                onConfirm={removeAllSavingSnips}
              >
                <Button
                  variant="ghost"
                  className="h-7 !px-2 text-sm font-normal text-[#F97066] hover:text-[#F97066]"
                >
                  {`Cancel`}
                </Button>
              </DeleteDoubleCheckDialog>
            ) : null} */}

            <Button
              onClick={() => setIsCollapsed(!isCollapsed)}
              variant="ghost"
              size="icon"
              className="h-7 w-7 text-caption-foreground"
            >
              {isCollapsed ? <ChevronDown size={17} /> : <ChevronUp size={17} />}
            </Button>
          </div>
        </div>
        <div className="relative w-full">
          <div
            className={cn(
              `overflow-y-auto pr-5 transition-all duration-300 ease-in-out`,
              isCollapsed ? 'max-h-0 opacity-0' : 'max-h-[200px] opacity-100',
              isCollapsed ? 'pb-2' : 'pb-4',
            )}
          >
            {[...savingSnips]
              // 倒序一下
              .reverse()
              .sort((a, b) => {
                // 确保 "saving" 状态的项目排在前面
                if (a.status === 'saving' && b.status !== 'saving') return -1;
                if (a.status !== 'saving' && b.status === 'saving') return 1;
                // 其他状态保持原有顺序
                return 0;
              })
              .filter((item) => item.status !== 'removed' && item.status !== 'hidden')
              .map((item) => (
                <SavingItem key={item.key} item={item} />
              ))}
          </div>
          <div className={styles.gradientMask}></div>
        </div>
      </div>
    </div>
  );
};
