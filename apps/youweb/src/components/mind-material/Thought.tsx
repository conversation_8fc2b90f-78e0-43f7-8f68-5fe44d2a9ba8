import type { Thought } from '@repo/common/types/thought/types';
import { cn } from '@repo/ui/lib/utils';
import { useAtom, useSetAtom } from 'jotai';
import { useCallback } from 'react';
import { userAtom } from '@/hooks/useUser';
import {
  addCommandToListAndOpenChatPanelAtom,
  addMessageToListAndOpenChatPanelAtom,
  askAIThoughtRealtimeSelectionAtom,
  getIsChatRunningAtom,
} from '../../hooks/ask-ai/useChatPanelState';
import { thoughtAtom, thoughtCurEditorAtom } from '../../hooks/scoped/useThoughts';
import { thoughtEditorAtom } from '../../hooks/useThought';
import { type ThoughtEditorReadyParams } from '@repo/editor-ui';
import { SelectionType } from '../thought/const';
import { ThoughtBody } from '../thought/thought-body';
import type { OnSelectionChangeParams } from '../thought/type';

export interface ThoughtProps {
  className?: string;
}

export default function ThoughtMaterial({ className }: ThoughtProps) {
  const [thought, setThoughtDetail] = useAtom(thoughtAtom);
  const [, setThoughtRealtimeSelection] = useAtom(askAIThoughtRealtimeSelectionAtom);
  const [, setCurEditor] = useAtom(thoughtCurEditorAtom);
  const [, setThoughtEditor] = useAtom(thoughtEditorAtom);
  const [, setAddCommandToListAndOpenChatPanel] = useAtom(addCommandToListAndOpenChatPanelAtom);
  const hasPendingCommand = useSetAtom(getIsChatRunningAtom);
  const [, setAddMessageToListAndOpenChatPanel] = useAtom(addMessageToListAndOpenChatPanelAtom);
  const [user] = useAtom(userAtom);

  const handleSelectionChange = useCallback(
    ({ plainText, selectionType }: OnSelectionChangeParams) => {
      if (selectionType === SelectionType.SELECTION) {
        setThoughtRealtimeSelection(plainText);
      } else {
        setThoughtRealtimeSelection('');
      }
    },
    [setThoughtRealtimeSelection],
  );

  const onThoughtUpdate = (data: Partial<Thought>) => {
    const newThought = { ...thought, ...data };
    setThoughtDetail(newThought);
  };

  const handleSetThoughtEditor = (editorReadyParams: ThoughtEditorReadyParams) => {
    setCurEditor(editorReadyParams.editor);
    setThoughtEditor(editorReadyParams.editor);
  };

  if (!thought) {
    return null;
  }

  return (
    <div className={cn('py-10 h-full', className)}>
      <ThoughtBody
        id={thought.id}
        thought={thought as unknown as Thought}
        onUpdate={onThoughtUpdate}
        onSelectionChange={handleSelectionChange}
        user={user}
        aiOptions={{
          setAddCommandToListAndOpenChatPanel,
          setAddMessageToListAndOpenChatPanel,
          hasPendingCommand: () => hasPendingCommand(),
        }}
        onReady={handleSetThoughtEditor}
      />
    </div>
  );
}
