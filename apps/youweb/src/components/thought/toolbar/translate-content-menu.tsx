import { type LanguageEnum, NormalLanguageList } from '@repo/common/types';
import { EditCommandTypeEnum, TranslateModeEnum } from '@repo/common/types/chat';
import { Button } from '@repo/ui/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@repo/ui/components/ui/dropdown-menu';
import { Tabs, TabsList, TabsTrigger } from '@repo/ui/components/ui/tabs';
import { sendTrackEvent } from '@repo/ui/lib/posthog/utils';
import type { Editor } from '@tiptap/react';
import { Check, Search, X } from 'lucide-react';
import { type ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { THOUGHT_EVENT_REPORT_KEY } from '../const';
import { invokeAICommand } from '../utils';

interface TranslateContentMenuProps {
  editor: Editor;
  children?: ReactNode | undefined;
  onOpenChange?: (open: boolean) => void;
  onItemClick?: (params: { language: string; mode: TranslateModeEnum }) => void;
}

const modeList = [
  {
    label: 'Replace',
    value: TranslateModeEnum.REPLACE,
  },
  {
    label: 'Insert',
    value: TranslateModeEnum.INSERT,
  },
  {
    label: 'Append',
    value: TranslateModeEnum.APPEND,
  },
];

// localStorage 键名
const TRANSLATE_SETTINGS_KEY = 'translate_content_settings';

interface TranslateSettings {
  language: string;
  mode: TranslateModeEnum;
}

export const TranslateContentMenu = (props: TranslateContentMenuProps) => {
  const { editor, children, onOpenChange, onItemClick } = props;

  const [open, setOpen] = useState(false);
  const [curLang, setCurLang] = useState<string>('');
  const [searchLang, setSearchLang] = useState<string>('');
  const [mode, setMode] = useState<TranslateModeEnum>(TranslateModeEnum.REPLACE);

  // 语言列表容器的引用
  const languageListRef = useRef<HTMLDivElement>(null);

  // 从 localStorage 读取设置
  const loadSettings = useCallback(() => {
    try {
      const stored = localStorage.getItem(TRANSLATE_SETTINGS_KEY);
      if (stored) {
        const settings: TranslateSettings = JSON.parse(stored);
        if (settings.language && settings.mode) {
          setCurLang(settings.language);
          setMode(settings.mode);
          return settings;
        }
      }
    } catch (error) {
      console.error('Failed to load translate settings:', error);
    }
    return null;
  }, []);

  // 保存设置到 localStorage
  const saveSettings = useCallback((language: string, mode: TranslateModeEnum) => {
    try {
      const settings: TranslateSettings = { language, mode };
      localStorage.setItem(TRANSLATE_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save translate settings:', error);
    }
  }, []);

  // 滚动到选中的语言
  const scrollToSelectedLanguage = useCallback((language: string) => {
    // 使用 setTimeout 确保 DOM 已更新
    setTimeout(() => {
      if (!languageListRef.current || !language) return;
      const container = languageListRef.current;
      if (!container) return;

      const selectedElement = container.querySelector(
        `[data-language="${language}"]`,
      ) as HTMLElement;
      if (!selectedElement) return;

      selectedElement.scrollIntoView({
        block: 'center',
      });
    }, 50);
  }, []);

  // 组件初始化时加载设置
  useEffect(() => {
    const settings = loadSettings();
    if (settings) {
      // 滚动到选中的语言（只在有设置且菜单打开时）
      if (open && settings.language) {
        scrollToSelectedLanguage(settings.language);
      }
    }
  }, [loadSettings, open, scrollToSelectedLanguage]);

  // 过滤语言列表
  const filteredLanguages = useMemo(() => {
    if (!searchLang.trim()) {
      return NormalLanguageList;
    }

    const searchTerm = searchLang.toLowerCase().trim();

    return NormalLanguageList.filter((item) => {
      // 搜索 label
      if (item.label.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // 搜索 value
      if (item.value.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // 搜索 aliases
      if (item.aliases) {
        return item.aliases.some((alias) => alias.toLowerCase().includes(searchTerm));
      }

      return false;
    });
  }, [searchLang]);

  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen);

      // 关闭时清除搜索状态
      if (!newOpen) {
        setSearchLang('');
      }

      onOpenChange?.(newOpen);
    },
    [onOpenChange],
  );

  const onClickTranslate = useCallback(() => {
    sendTrackEvent(THOUGHT_EVENT_REPORT_KEY.THOUGHT_TOOLBAR_TRANSLATE_CONTENT_MENU_CLICK, {
      language: curLang,
      mode,
    });

    // 保存用户设置到 localStorage
    saveSettings(curLang, mode);

    const res = invokeAICommand({
      editor,
      commandOrMessage: {
        type: EditCommandTypeEnum.TRANSLATE,
        target_language: curLang as LanguageEnum,
        mode: mode,
      },
    });
    if (res) {
      onItemClick?.({ language: curLang, mode });
      setOpen(false);
      onOpenChange?.(false);
    }
  }, [curLang, mode, onItemClick, onOpenChange, saveSettings]);

  const renderEmptyContent = () => {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <div className="text-sm text-muted-foreground">No results</div>
      </div>
    );
  };

  return (
    <DropdownMenu open={open} onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent className="w-[16.5rem]" side="left" align="start" sideOffset={18}>
        <div className="flex flex-col">
          <div className="px-4 pt-3 pb-1 text-xs text-muted-foreground">Translate to</div>
          <div className="px-3 mt-1 mb-1">
            <div className="flex h-8 items-center gap-[6px] rounded-lg bg-card-snips">
              <Search size={16} className="ml-2 text-caption" />
              <input
                placeholder="Search language"
                className="w-full text-xs font-normal bg-transparent placeholder:text-caption"
                type="text"
                value={searchLang}
                onChange={(e) => setSearchLang(e.target.value)}
              />
              {searchLang ? (
                <Button
                  size="icon"
                  variant="ghost"
                  className="flex-shrink-0 w-4 h-4 mr-2 rounded-full bg-card-snips text-muted-foreground hover:text-foreground"
                  onClick={() => setSearchLang('')}
                >
                  <X size={12} />
                </Button>
              ) : null}
            </div>
          </div>
          <div ref={languageListRef} className="flex max-h-[14.75rem] flex-col overflow-y-auto">
            {filteredLanguages.length > 0
              ? filteredLanguages.map((item) => (
                  <div
                    key={item.value}
                    data-language={item.value}
                    className="flex items-center justify-between py-2 pl-4 pr-5 text-sm cursor-pointer hover:bg-card-snips"
                    onClick={() => setCurLang(item.value)}
                  >
                    {item.label}
                    {curLang === item.value && <Check size={16} />}
                  </div>
                ))
              : renderEmptyContent()}
          </div>
          <div className="flex flex-col justify-center w-full px-4 pb-4 mt-1">
            <div className="mb-2 h-px w-[14.5rem] bg-muted" />
            <div className="mb-1 text-xs text-muted-foreground">Mode</div>
            <Tabs
              value={mode}
              onValueChange={(value: string) => setMode(value as TranslateModeEnum)}
            >
              <TabsList variant="default" className="w-full h-8">
                {modeList.map((item) => (
                  <TabsTrigger
                    key={item.value}
                    value={item.value}
                    variant="default"
                    className="text-xs"
                  >
                    {item.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
            <Button
              onClick={onClickTranslate}
              disabled={!curLang || !mode}
              className="w-full h-8 mt-4 rounded-full"
            >
              Translate
            </Button>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
