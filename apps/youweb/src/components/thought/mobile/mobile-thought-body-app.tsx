import type { Editor } from '@tiptap/react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { ThoughtNavigationMobile } from '@repo/editor-ui';
import { MobileThoughtBody, type MobileThoughtBodyProps } from './mobile-thought-body';

const URL_THOUGHT_ID_PARAM = 'thoughtId';

export const MobileThoughtBodyApp = (props: MobileThoughtBodyProps) => {
  const [editor, setEditor] = useState<Editor | null>(null);
  const appRef = useRef<HTMLDivElement>(null);
  const [searchParams] = useSearchParams();

  // 从 URL 参数获取 thoughtId，如果 props 中没有传入 id 的话
  const thoughtId = props.id || searchParams.get(URL_THOUGHT_ID_PARAM) || undefined;

  const focusEditor = useCallback(() => {
    editor?.commands.focus('end', { scrollIntoView: false });
  }, [editor]);

  useEffect(() => {
    document.body.style.backgroundColor = 'transparent';
    document.body.style.height = '100%';

    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1, user-scalable=no';
    document.head.appendChild(meta);

    // 添加点击事件监听器，检查点击是否在 MobileThoughtBodyApp 之外
    const handleBodyClick = (e: MouseEvent) => {
      if (appRef.current && !appRef.current.contains(e.target as Node)) {
        const appHeight = appRef.current.offsetHeight;
        const bodyHeight = document.body.offsetHeight;

        if (appHeight > bodyHeight) {
          return;
        }

        if (bodyHeight - appHeight <= 50) {
          return;
        }

        focusEditor();
      }
    };

    document.body.addEventListener('click', handleBodyClick);

    return () => {
      document.body.removeEventListener('click', handleBodyClick);
    };
  }, [focusEditor]);

  return (
    <div ref={appRef} className="w-full px-5 py-3 overflow-x-hidden bg-transparent">
      <MobileThoughtBody
        {...props}
        id={thoughtId}
        onCreate={(params) => {
          setEditor(params.editor);
          props.onCreate?.(params);
        }}
      />
      {editor && <ThoughtNavigationMobile editor={editor} />}
    </div>
  );
};
