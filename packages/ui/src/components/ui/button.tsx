'use client';

import { Slot } from '@radix-ui/react-slot';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { cn } from '@repo/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';
import * as React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-[40px] font-normal transition-all disabled:pointer-events-none  [&_svg]:pointer-events-none shrink-0 [&_svg]:shrink-0 outline-none aria-invalid:border-destructive disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed',
  {
    variants: {
      variant: {
        default: 'bg-brand text-brand-foreground hover:opacity-80 active:bg-brand',
        secondary: 'bg-muted text-foreground hover:bg-card-snips active:bg-divider',
        outline:
          'border bg-card hover:bg-card-snips active:bg-muted hover:text-accent-foreground disabled:bg-card-snips disabled:text-muted-foreground disabled:border-bg-border',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/80 active:bg-destructive',
        ghost:
          'bg-transparent hover:bg-card-snips active:bg-muted hover:text-accent-foreground disabled:text-muted-foreground',
        icon: '!p-1 text-accent-foreground hover:bg-card-snips active:bg-muted hover:text-accent-foreground disabled:text-muted-foreground rounded-md',
      },
      size: {
        default: 'text-sm leading-5 gap-1 [&_svg]:size-[16px]',
        sm: 'text-xs leading-4.5 gap-0.5 [&_svg]:size-[14px]',
        xs: 'text-xs leading-4.5 gap-0.5 [&_svg]:size-[12px]',
        xxs: 'text-xs leading-4.5 gap-0.5 [&_svg]:size-[10px]',
        lg: 'text-base leading-6 gap-1.5 [&_svg]:size-[20px]',
        icon: 'h-10 w-10',
      },
      iconOnly: {
        true: '',
        false: '',
      },
    },
    compoundVariants: [
      {
        variant: 'icon',
        className: 'gap-0',
      },
      // iconOnly false - normal button padding
      {
        iconOnly: false,
        size: 'default',
        className: 'py-1.5 px-4 has-[>svg]:px-3',
      },
      {
        iconOnly: false,
        size: 'sm',
        className: 'py-[3px] px-2.5 has-[>svg]:px-2',
      },
      {
        iconOnly: false,
        size: 'xs',
        className: 'py-[3px] px-2.5 has-[>svg]:px-2',
      },
      {
        iconOnly: false,
        size: 'xxs',
        className: 'py-[3px] px-2.5 has-[>svg]:px-2',
      },
      {
        iconOnly: false,
        size: 'lg',
        className: 'py-2 px-6 has-[>svg]:px-5',
      },
      // iconOnly true - square button padding
      {
        iconOnly: true,
        size: 'sm',
        className: 'p-[3px]',
      },
      {
        iconOnly: true,
        size: 'xs',
        className: 'p-[3px]',
      },
      {
        iconOnly: true,
        size: 'xxs',
        className: 'p-[3px]',
      },
      {
        iconOnly: true,
        size: 'default',
        className: 'p-1.5',
      },
      {
        iconOnly: true,
        size: 'lg',
        className: 'p-2',
      },
    ],
    defaultVariants: {
      variant: 'default',
      size: 'default',
      iconOnly: false,
    },
  },
);

export interface ButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'onClick'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  async?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => any | Promise<any>;
  trackEventName?: string;
  trackEventParams?: Record<string, any>;
  iconSize?: number | string;
  iconOnly?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading: loadingProp,
      async = false,
      children,
      onClick,
      trackEventName,
      trackEventParams,
      disabled,
      iconSize,
      iconOnly,
      ...props
    },
    ref,
  ) => {
    const [internalLoading, setInternalLoading] = React.useState(false);
    const loading = loadingProp ?? internalLoading;

    const { trackButtonClick } = useTrackActions();

    const handleClick = React.useCallback(
      async (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
        if (trackEventName) {
          trackButtonClick(trackEventName, trackEventParams);
        }

        if (!onClick) return;

        if (async) {
          try {
            setInternalLoading(true);
            const result = onClick(event);
            if (result && typeof result === 'object' && 'then' in result) {
              await result;
            }
          } catch (error) {
            console.error('Button async handler error:', error);
          } finally {
            setInternalLoading(false);
          }
        } else {
          onClick(event);
        }
      },
      [onClick, async, trackEventName, trackEventParams, trackButtonClick],
    );

    const Comp = asChild ? Slot : 'button';

    const iconSizeStyles = iconSize
      ? ({
          '--icon-size': typeof iconSize === 'number' ? `${iconSize}px` : iconSize,
        } as React.CSSProperties)
      : {};

    const dynamicIconSizeClass = iconSize ? '[&_svg]:!size-[var(--icon-size)]' : '';

    return (
      <Comp
        data-slot="button"
        className={cn(buttonVariants({ variant, size, iconOnly, className }), dynamicIconSizeClass)}
        style={iconSizeStyles}
        ref={ref}
        disabled={disabled || loading}
        onClick={handleClick}
        {...props}
      >
        {loading && <Loader2 className="mr-2 animate-spin" />}
        {children}
      </Comp>
    );
  },
);
Button.displayName = 'Button';

const ButtonWithTooltip = React.forwardRef<
  HTMLButtonElement,
  { tooltip: React.ReactNode } & ButtonProps
>((props, ref) => {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>
          <Button variant="ghost" className={'h-5 w-5 rounded-full'} {...props} ref={ref} />
        </TooltipTrigger>
        <TooltipContent>{props.tooltip}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
});
ButtonWithTooltip.displayName = 'ButtonWithTooltip';

export { Button, buttonVariants, ButtonWithTooltip };
