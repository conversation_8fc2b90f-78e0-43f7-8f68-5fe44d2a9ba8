'use client';

import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';
import { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';
import * as React from 'react';
import { cn } from '../../lib/utils';
import { Input } from './input';

// 类型定义 - 使用 unknown 作为基础类型，通过泛型 hook 提供类型安全
interface DropdownMenuSearchContextValue {
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  filter?: (value: unknown, searchQuery: string) => boolean;
  searchInputRef?: React.RefObject<HTMLInputElement>;
  isNavigating: boolean;
  setIsNavigating: (navigating: boolean) => void;
}

// 全局 Context
const DropdownMenuSearchContext = React.createContext<DropdownMenuSearchContextValue | null>(null);

// 类型安全的 hook - 通过泛型参数提供类型推断
function useDropdownMenuSearch<T = unknown>() {
  const context = React.useContext(DropdownMenuSearchContext);
  if (!context) {
    return {
      searchQuery: '',
      setSearchQuery: () => {},
      filter: undefined as ((value: T, searchQuery: string) => boolean) | undefined,
      searchInputRef: undefined as React.RefObject<HTMLInputElement> | undefined,
      isNavigating: false,
      setIsNavigating: () => {},
    };
  }

  // 直接返回带有正确类型的对象
  return {
    searchQuery: context.searchQuery,
    setSearchQuery: context.setSearchQuery,
    filter: context.filter as ((value: T, searchQuery: string) => boolean) | undefined,
    searchInputRef: context.searchInputRef,
    isNavigating: context.isNavigating,
    setIsNavigating: context.setIsNavigating,
  };
}

function DropdownMenu({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {
  return <DropdownMenuPrimitive.Root data-slot="dropdown-menu" {...props} />;
}

function DropdownMenuPortal({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {
  return <DropdownMenuPrimitive.Portal data-slot="dropdown-menu-portal" {...props} />;
}

function DropdownMenuTrigger({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {
  return <DropdownMenuPrimitive.Trigger data-slot="dropdown-menu-trigger" {...props} />;
}

function DropdownMenuContent<T = unknown>({
  className,
  sideOffset = 4,
  container,
  filter,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Content> & {
  container?: HTMLElement | null;
  filter?: (value: T, searchQuery: string) => boolean;
}) {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [isNavigating, setIsNavigating] = React.useState(false);
  const searchInputRef = React.useRef<HTMLInputElement>(null);

  const contextValue = React.useMemo<DropdownMenuSearchContextValue>(
    () => ({
      searchQuery,
      setSearchQuery,
      filter: filter as ((value: unknown, searchQuery: string) => boolean) | undefined,
      searchInputRef,
      isNavigating,
      setIsNavigating,
    }),
    [searchQuery, filter, isNavigating],
  );

  const content = (
    <DropdownMenuPrimitive.Content
      data-slot="dropdown-menu-content"
      sideOffset={sideOffset}
      className={cn(
        // 基础布局和定位
        'z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin)',
        // 尺寸和溢出处理
        'max-h-(--radix-dropdown-menu-content-available-height) overflow-x-hidden overflow-y-auto',
        // 外观样式
        'rounded-2xl p-2 shadow-lg border-none',
        // 颜色主题
        'bg-popover text-popover-foreground',
        // 动画状态
        'data-[state=open]:animate-in data-[state=closed]:animate-out',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
        'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
        // 滑入动画方向
        'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2',
        'data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
        className,
      )}
      {...props}
    />
  );

  return (
    <DropdownMenuPrimitive.Portal container={container}>
      <DropdownMenuSearchContext.Provider value={contextValue}>
        {content}
      </DropdownMenuSearchContext.Provider>
    </DropdownMenuPrimitive.Portal>
  );
}

function DropdownMenuGroup({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {
  return <DropdownMenuPrimitive.Group data-slot="dropdown-menu-group" {...props} />;
}

function traverseNode(node: React.ReactNode, strings: string[]): void {
  if (typeof node === 'string') {
    strings.push(node);
  } else if (typeof node === 'number') {
    strings.push(node.toString());
  } else if (React.isValidElement(node)) {
    if (node.props.children) {
      traverseNode(node.props.children, strings);
    }
  } else if (Array.isArray(node)) {
    node.forEach((child) => traverseNode(child, strings));
  }
}

function extractStringsFromChildren(children: React.ReactNode): string[] {
  const strings: string[] = [];
  traverseNode(children, strings);
  return strings;
}

function DropdownMenuItem<T = unknown>({
  className,
  inset,
  variant = 'default',
  searchValue,
  customSvg = false,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {
  inset?: boolean;
  variant?: 'default' | 'destructive';
  searchValue?: T;
  customSvg?: boolean;
}) {
  const { searchQuery, filter, searchInputRef, isNavigating } = useDropdownMenuSearch<T>();

  const shouldShow = React.useMemo(() => {
    if (!searchQuery) return true;
    if (!filter) {
      const strings = extractStringsFromChildren(props.children);
      if (searchValue?.toString()) {
        strings.push(searchValue.toString());
      }
      const query = searchQuery.toLowerCase();
      return strings.some((str) => str.toLowerCase().includes(query));
    }
    if (searchValue === undefined) return false;
    return filter(searchValue, searchQuery);
  }, [searchQuery, filter, searchValue, props.children]);

  if (!shouldShow) {
    return null;
  }

  // 处理焦点事件，防止在搜索时自动获得焦点（但允许键盘导航）
  const handleFocus = (e: React.FocusEvent) => {
    if (searchQuery && searchInputRef?.current && !isNavigating) {
      e.preventDefault();
      // 将焦点重新定向回搜索框
      searchInputRef.current.focus();
    }
  };

  return (
    <DropdownMenuPrimitive.Item
      data-slot="dropdown-menu-item"
      data-inset={inset}
      data-variant={variant}
      className={cn(
        // 基础布局
        'relative flex cursor-pointer items-center gap-2 text-sm text-foreground',
        // 尺寸和间距
        'rounded-lg px-2 py-1.5 text-sm font-normal',
        // 交互状态
        'cursor-pointer outline-hidden select-none',
        // Hover 状态
        'hover:bg-card-snips',
        // 焦点状态
        'focus:bg-accent focus:text-accent-foreground',
        // 禁用状态
        'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
        // 缩进样式
        'data-[inset]:pl-8',
        // SVG图标样式
        !customSvg && '[&_svg]:pointer-events-none [&_svg]:shrink-0',
        !customSvg && "[&_svg:not([class*='size-'])]:size-4",
        // 危险变体样式
        'data-[variant=destructive]:text-destructive',
        'data-[variant=destructive]:focus:text-destructive',
        'data-[variant=destructive]:focus:bg-destructive/10',
        'dark:data-[variant=destructive]:focus:bg-destructive/20',
        'data-[variant=destructive]:*:[svg]:!text-destructive',
        className,
      )}
      onFocus={handleFocus}
      {...props}
    />
  );
}

export type DropdownMenuSearchItemProps = {
  placeholder?: string;
  className?: string;
};

function DropdownMenuSearchItem({
  placeholder = 'Search...',
  className,
}: DropdownMenuSearchItemProps) {
  const { searchQuery, setSearchQuery, searchInputRef, setIsNavigating } = useDropdownMenuSearch();

  React.useEffect(() => {
    return () => {
      setSearchQuery('');
      setIsNavigating(false);
    };
  }, [setSearchQuery, setIsNavigating]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
      // 允许键盘导航，设置导航状态
      setIsNavigating(true);
      // 让 Radix UI 处理键盘导航
      // 不调用 preventDefault，让事件继续传播
    } else {
      // 其他键盘事件时重置导航状态
      setIsNavigating(false);
    }
  };

  const handleFocus = () => {
    // 当搜索框重新获得焦点时，重置导航状态
    setIsNavigating(false);
  };

  return (
    <div className={cn('w-full', className)}>
      <Input
        ref={searchInputRef}
        variant="search"
        placeholder={placeholder}
        value={searchQuery}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        className="h-8"
      />
    </div>
  );
}

function DropdownMenuCheckboxItem<T = unknown>({
  className,
  children,
  checked,
  searchValue,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem> & {
  searchValue?: T;
}) {
  const { searchQuery, filter, searchInputRef, isNavigating } = useDropdownMenuSearch<T>();

  const shouldShow = React.useMemo(() => {
    if (!searchQuery) return true;
    if (!filter) {
      const strings = extractStringsFromChildren(children);
      if (searchValue?.toString()) {
        strings.push(searchValue.toString());
      }
      const query = searchQuery.toLowerCase();
      return strings.some((str) => str.toLowerCase().includes(query));
    }
    if (searchValue === undefined) return false;
    return filter(searchValue, searchQuery);
  }, [searchQuery, filter, searchValue, children]);

  if (!shouldShow) {
    return null;
  }

  // 处理焦点事件，防止在搜索时自动获得焦点（但允许键盘导航）
  const handleFocus = (e: React.FocusEvent) => {
    if (searchQuery && searchInputRef?.current && !isNavigating) {
      e.preventDefault();
      // 将焦点重新定向回搜索框
      searchInputRef.current.focus();
    }
  };

  return (
    <DropdownMenuPrimitive.CheckboxItem
      key={searchValue?.toString() || ''}
      data-slot="dropdown-menu-checkbox-item"
      className={cn(
        // 基础布局
        'relative flex cursor-default items-center text-sm font-normal cursor-pointer gap-1',
        // 尺寸和间距
        'rounded-md py-1.5 pl-2 pr-8 text-sm',
        // 交互状态
        'outline-hidden select-none',
        // 焦点状态
        'focus:bg-accent focus:text-accent-foreground',
        // Hover 状态
        'hover:bg-card-snips',
        // 禁用状态
        'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
        // SVG图标样式
        "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className,
      )}
      checked={checked}
      onFocus={handleFocus}
      {...props}
    >
      {children}
      <span className="pointer-events-none absolute right-2 flex size-3.5 items-center justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CheckIcon className="size-4" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
    </DropdownMenuPrimitive.CheckboxItem>
  );
}

function DropdownMenuRadioGroup({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {
  return <DropdownMenuPrimitive.RadioGroup data-slot="dropdown-menu-radio-group" {...props} />;
}

function DropdownMenuRadioItem<T = unknown>({
  className,
  children,
  searchValue,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem> & {
  searchValue?: T;
}) {
  const { searchQuery, filter, searchInputRef, isNavigating } = useDropdownMenuSearch<T>();

  const shouldShow = React.useMemo(() => {
    if (!searchQuery) return true;
    if (!filter) {
      const strings = extractStringsFromChildren(children);
      if (searchValue?.toString()) {
        strings.push(searchValue.toString());
      }
      const query = searchQuery.toLowerCase();
      return strings.some((str) => str.toLowerCase().includes(query));
    }
    if (searchValue === undefined) return false;
    return filter(searchValue, searchQuery);
  }, [searchQuery, filter, searchValue, children]);

  if (!shouldShow) {
    return null;
  }

  // 处理焦点事件，防止在搜索时自动获得焦点（但允许键盘导航）
  const handleFocus = (e: React.FocusEvent) => {
    if (searchQuery && searchInputRef?.current && !isNavigating) {
      e.preventDefault();
      // 将焦点重新定向回搜索框
      searchInputRef.current.focus();
    }
  };

  return (
    <DropdownMenuPrimitive.RadioItem
      data-slot="dropdown-menu-radio-item"
      className={cn(
        // 基础布局
        'relative flex cursor-default items-center gap-2 cursor-pointer',
        // 尺寸和间距
        'rounded-sm py-1.5 pr-2 pl-8 text-sm',
        // 交互状态
        'outline-hidden select-none',
        // Hover 状态
        'hover:bg-card-snips',
        // 焦点状态
        'focus:bg-accent focus:text-accent-foreground',
        // 禁用状态
        'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
        // SVG图标样式
        "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className,
      )}
      onFocus={handleFocus}
      {...props}
    >
      <span className="pointer-events-none absolute left-2 flex size-3.5 items-center justify-center">
        <DropdownMenuPrimitive.ItemIndicator>
          <CircleIcon className="fill-current size-2" />
        </DropdownMenuPrimitive.ItemIndicator>
      </span>
      {children}
    </DropdownMenuPrimitive.RadioItem>
  );
}

function DropdownMenuLabel({
  className,
  inset,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {
  inset?: boolean;
}) {
  return (
    <DropdownMenuPrimitive.Label
      data-slot="dropdown-menu-label"
      data-inset={inset}
      className={cn('px-2 py-1.5 text-sm font-medium data-[inset]:pl-8', className)}
      {...props}
    />
  );
}

function DropdownMenuSeparator({
  className,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {
  return (
    <div className="w-full px-2">
      <DropdownMenuPrimitive.Separator
        data-slot="dropdown-menu-separator"
        className={cn('bg-border my-1 h-px bg-muted', className)}
        {...props}
      />
    </div>
  );
}

function DropdownMenuShortcut({ className, ...props }: React.ComponentProps<'span'>) {
  return (
    <span
      data-slot="dropdown-menu-shortcut"
      className={cn('text-caption-foreground ml-auto text-xs', 'tracking-[1px]', className)}
      {...props}
    />
  );
}

function DropdownMenuSub({ ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {
  return <DropdownMenuPrimitive.Sub data-slot="dropdown-menu-sub" {...props} />;
}

function DropdownMenuSubTrigger({
  className,
  inset,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {
  inset?: boolean;
}) {
  return (
    <DropdownMenuPrimitive.SubTrigger
      data-slot="dropdown-menu-sub-trigger"
      data-inset={inset}
      className={cn(
        // 基础布局
        'flex gap-2 items-center cursor-pointer',
        // 尺寸和间距
        'rounded-md px-2 py-1.5 text-sm',
        // 交互状态
        'outline-hidden select-none',
        // 焦点状态
        'focus:bg-accent focus:text-accent-foreground',
        // 打开状态
        'data-[state=open]:bg-accent data-[state=open]:text-accent-foreground',
        // 缩进样式
        'data-[inset]:pl-8',
        className,
      )}
      {...props}
    >
      {children}
      <ChevronRightIcon className="ml-auto size-4" />
    </DropdownMenuPrimitive.SubTrigger>
  );
}

function DropdownMenuSubContent({
  className,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {
  return (
    <DropdownMenuPrimitive.SubContent
      data-slot="dropdown-menu-sub-content"
      className={cn(
        // 基础布局和定位
        'z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin)',
        // 溢出处理
        'overflow-hidden',
        // 外观样式
        'rounded-md border p-1 shadow-lg',
        // 颜色主题
        'bg-popover text-popover-foreground',
        // 动画状态
        'data-[state=open]:animate-in data-[state=closed]:animate-out',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
        'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
        // 滑入动画方向
        'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2',
        'data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
        className,
      )}
      {...props}
    />
  );
}

export {
  DropdownMenu,
  DropdownMenuPortal,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuSearchItem,
};
