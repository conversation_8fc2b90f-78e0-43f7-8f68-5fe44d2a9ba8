import { createDocByBase64, createDocByJSONContent } from '@repo/editor-common';
import { cn } from '@repo/ui/lib/utils';
import { EditorContent, useEditor } from '@tiptap/react';
import { useState } from 'react';
import { IndexeddbPersistence } from 'y-indexeddb';
import { Doc } from 'yjs';
import { getThoughtEditorExtensions } from './extension';
import type { ThoughtEditorProps } from './type';

export const ThoughtEditor = (props: ThoughtEditorProps) => {
  const { id, className, content, onReady, storeOptions } = props;
  const { localIndexDB = true } = storeOptions || {};

  const [ydoc, _] = useState<Doc>(() => {
    try {
      const doc =
        typeof content === 'string' ? createDocByBase64(content) : createDocByJSONContent(content);
      if (localIndexDB) {
        new IndexeddbPersistence(id, doc);
      }
      return doc;
    } catch (_) {
      const doc = new Doc();
      if (localIndexDB) {
        new IndexeddbPersistence(id, doc);
      }
      return doc;
    }
  });

  const editor = useEditor(
    {
      immediatelyRender: true,
      editable: props.editorOptions?.editable ?? true,
      editorProps: {
        ...props.editorOptions?.editorProps,
        attributes: {
          spellcheck: 'false',
          ...props.editorOptions?.editorProps?.attributes,
        },
        // 固有动作，拦截按下 ctrl + s
        handleKeyDown: (_, event) => {
          if (event.key === 's' && (event.metaKey || event.ctrlKey)) {
            event.preventDefault();
          }
          props.editorOptions?.editorProps?.handleKeyDown?.(_, event);
        },
      },
      extensions: getThoughtEditorExtensions({
        id,
        options: props.extensionOptions || {},
        ydoc,
      }),
      onCreate: () => {
        onReady?.({ editor, ydoc });
      },
    },
    [ydoc],
  );

  return (
    <div className={cn('w-full h-full', className)}>
      <EditorContent editor={editor} />
    </div>
  );
};
