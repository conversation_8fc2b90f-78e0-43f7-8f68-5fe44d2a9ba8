import { DOC_FRAGMENT_FIELED } from '@repo/editor-common';
import { LocalCollaboration, SlashCommand } from '../extension';
import type { AnyExtension } from '@tiptap/core';
import { Collaboration } from '@tiptap/extension-collaboration';
import type { Doc } from 'yjs';
import { getCommonFunctionExtensionList } from './extensions/function';
import { getMarkExtensionList } from './extensions/mark';
import { getNodeExtensionList } from './extensions/node';
import { ThoughtAdapter } from './extensions/thought-adapter-extension';
import type { EditorExtensionOptions } from './type';

export type ThoughtEditorExtensionConfig = {
  id: string;
  options: EditorExtensionOptions;
  ydoc: Doc;
};

export const getThoughtEditorExtensions = (config: ThoughtEditorExtensionConfig): AnyExtension[] => {
  const { options, ydoc, id } = config;
  return [
    ...getCommonFunctionExtensionList(options),
    ...getMarkExtensionList(options),
    ...getNodeExtensionList(options),
    Collaboration.configure({
      document: ydoc,
      field: DOC_FRAGMENT_FIELED,
    }),
    ThoughtAdapter.configure({
      id,
      localInfoEnable: true,
    }),
    LocalCollaboration.configure({
      document: ydoc,
      field: DOC_FRAGMENT_FIELED,
    }),
    SlashCommand,
  ].filter(Boolean);
};
