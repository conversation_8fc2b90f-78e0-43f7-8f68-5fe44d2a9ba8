import type { DiffBlockOptions, LinkOptions, MermaidOptions } from '@repo/editor-common';
import type {
  CharacterCountOptions,
  ImageExtensionOptions,
  KeyDownOptions,
  LocalCollaborationOptions,
} from '../extension';
import type { PlaceholderOptions } from '@tiptap/extension-placeholder';
import type { Editor, JSONContent, UseEditorOptions } from '@tiptap/react';
import type { Doc } from 'yjs';

export type EditorExtensionOptions = {
  keyDownOptions?: KeyDownOptions;
  characterCountOptions?: CharacterCountOptions;
  imageOptions?: Partial<ImageExtensionOptions>;
  localCollaborationOptions?: LocalCollaborationOptions;
  linkOptions?: Partial<LinkOptions>;
  placeholderOptions?: Partial<PlaceholderOptions>;
  diffBlockOptions?: Partial<DiffBlockOptions>;
  mermaidOptions?: Partial<MermaidOptions>;
};

export type ThoughtEditorProps = {
  id: string;
  className?: string;
  content: string | JSONContent;
  onReady?: (params: ThoughtEditorReadyParams) => void;
  storeOptions?: {
    localIndexDB?: boolean;
  };
  // 不允许传入 extesnion
  editorOptions?: Omit<UseEditorOptions, 'extensions'>;
  extensionOptions?: EditorExtensionOptions;
};

export type ThoughtEditorReadyParams = {
  editor: Editor;
  ydoc: Doc;
};

export type GetThoughtExtensionConfig = {
  options: EditorExtensionOptions;
  ydoc: Doc;
};
