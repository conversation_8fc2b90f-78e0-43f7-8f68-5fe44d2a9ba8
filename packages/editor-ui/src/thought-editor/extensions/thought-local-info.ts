import type { Editor } from '@tiptap/core';
import { debounce } from 'lodash-es';

interface ThoughtPosition {
  cursor: number | null;
  scroll: {
    top: number;
  };
  lastUpdated: number;
}

interface ThoughtPositionsStorage {
  positions: Record<string, ThoughtPosition>;
}

interface ThoughtLocalInfoOptions {
  id: string;
  editor: Editor;
}

const STORAGE_KEY = '$_youmind-thought-local-info-positions';
const MAX_STORED_THOUGHTS = 50;

export class ThoughtLocalInfo {
  private id: string;
  private editor: Editor;
  private scrollContainer: HTMLElement | null = null;
  private debouncedSavePosition: ReturnType<typeof debounce>;
  private debouncedSaveScroll: ReturnType<typeof debounce>;

  constructor(options: ThoughtLocalInfoOptions) {
    this.id = options.id;
    this.editor = options.editor;

    this.debouncedSavePosition = debounce(this.savePosition.bind(this), 1000);
    this.debouncedSaveScroll = debounce(this.saveScroll.bind(this), 800);
  }

  private findScrollContainer(element: HTMLElement): HTMLElement | null {
    let parent = element.parentElement;

    while (parent) {
      const { overflowY } = window.getComputedStyle(parent);
      if (
        ['auto', 'scroll', 'overlay'].includes(overflowY) &&
        parent.scrollHeight > parent.clientHeight
      ) {
        return parent;
      }
      parent = parent.parentElement;
    }

    return document.documentElement.scrollHeight > window.innerHeight
      ? document.documentElement
      : null;
  }

  private getStoredPositions(): ThoughtPositionsStorage {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : { positions: {} };
    } catch {
      return { positions: {} };
    }
  }

  private saveStoredPositions(storage: ThoughtPositionsStorage): void {
    try {
      // 限制存储的想法数量，删除最旧的记录
      const positions = storage.positions;
      const entries = Object.entries(positions);
      
      if (entries.length > MAX_STORED_THOUGHTS) {
        // 按最后更新时间排序，保留最新的记录
        entries.sort((a, b) => b[1].lastUpdated - a[1].lastUpdated);
        const keepEntries = entries.slice(0, MAX_STORED_THOUGHTS);
        storage.positions = Object.fromEntries(keepEntries);
      }

      localStorage.setItem(STORAGE_KEY, JSON.stringify(storage));
    } catch {
      // 存储失败时忽略错误
    }
  }

  private savePosition(): void {
    if (typeof window === 'undefined') return;

    const { selection } = this.editor.state;
    const storage = this.getStoredPositions();
    
    storage.positions[this.id] = {
      ...storage.positions[this.id],
      cursor: selection.from,
      lastUpdated: Date.now(),
    };

    this.saveStoredPositions(storage);
  }

  private saveScroll(): void {
    if (typeof window === 'undefined' || !this.scrollContainer) return;

    const storage = this.getStoredPositions();
    
    storage.positions[this.id] = {
      ...storage.positions[this.id],
      scroll: {
        top: this.scrollContainer.scrollTop,
      },
      lastUpdated: Date.now(),
    };

    this.saveStoredPositions(storage);
  }

  public setupEventListeners(): void {
    if (typeof window === 'undefined') return;

    // 找到滚动容器
    const editorElement = this.editor.view.dom as HTMLElement;
    this.scrollContainer = this.findScrollContainer(editorElement);

    // 监听光标位置变化
    this.editor.on('selectionUpdate', () => {
      this.debouncedSavePosition();
    });

    // 监听滚动事件
    if (this.scrollContainer) {
      this.scrollContainer.addEventListener('scroll', () => {
        this.debouncedSaveScroll();
      }, { passive: true });
    }
  }

  public tryRecoverPosition(): void {
    if (typeof window === 'undefined') return;

    const storage = this.getStoredPositions();
    const position = storage.positions[this.id];

    if (!position) return;

    // 恢复光标位置
    if (position.cursor !== null) {
      try {
        const docSize = this.editor.state.doc.content.size;
        const safePos = Math.min(position.cursor, docSize);
        this.editor.commands.setTextSelection(safePos);
      } catch {
        // 恢复失败时忽略错误
      }
    }

    // 恢复滚动位置
    if (this.scrollContainer && position.scroll) {
      setTimeout(() => {
        if (this.scrollContainer) {
          this.scrollContainer.scrollTop = position.scroll.top;
        }
      }, 100);
    }
  }

  public destroy(): void {
    this.debouncedSavePosition.cancel();
    this.debouncedSaveScroll.cancel();
  }
}
