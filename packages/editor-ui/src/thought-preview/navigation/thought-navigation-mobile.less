.thought-navigation-mobile {
  &__preview {
    transition: opacity 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                transform 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: center left;
    
    &--hidden {
      opacity: 0;
      transform: translateX(12px) translateY(-2px) scale(0.9);
      pointer-events: none;
    }
    
    &--visible {
      opacity: 1;
      transform: translateX(0) translateY(0) scale(1);
    }
  }
  
  &__detail {
    transition: opacity 250ms cubic-bezier(0.16, 1, 0.3, 1), 
                transform 250ms cubic-bezier(0.16, 1, 0.3, 1);
    transform-origin: top right;
    
    &--hidden {
      opacity: 0;
      transform: translateY(-50%) translateX(12px) scale(0.92);
      pointer-events: none;
    }
    
    &--visible {
      opacity: 1;
      transform: translateY(-50%) translateX(0) scale(1);
    }
  }

  position: relative;
}
