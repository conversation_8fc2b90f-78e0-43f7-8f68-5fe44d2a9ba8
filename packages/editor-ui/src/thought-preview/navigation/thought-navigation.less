.thought-navigation {
  &__preview {
    transition: opacity 350ms cubic-bezier(0.4, 0, 0.2, 1), 
                transform 350ms cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center right;
    
    &--hidden {
      opacity: 0;
      transform: translateX(12px) translateY(-4px) scale(1.15);
      pointer-events: none;
    }
    
    &--visible {
      opacity: 1;
      transform: translateX(0) translateY(0) scale(1);
    }
  }
  
  &__detail {
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1) 50ms, 
                transform 400ms cubic-bezier(0.4, 0, 0.2, 1) 50ms;
    transform-origin: center left;
    
    &--hidden {
      opacity: 0;
      transform: translateY(-50%) translateX(-8px) scale(0.85);
      pointer-events: none;
    }
    
    &--visible {
      opacity: 1;
      transform: translateY(-50%) translateX(0) scale(1);
    }
  }

  position: relative;
}
