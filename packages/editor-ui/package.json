{"name": "@repo/editor-ui", "version": "1.0.0", "private": true, "type": "module", "exports": {".": "./src/index.ts"}, "scripts": {"lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format ."}, "dependencies": {"react": "catalog:", "react-dom": "catalog:", "@tiptap/core": "catalog:", "@tiptap/react": "catalog:", "@tiptap/pm": "catalog:", "date-fns": "catalog:", "yjs": "catalog:", "lucide-react": "catalog:", "highlight.js": "catalog:", "tailwind-merge": "catalog:", "lowlight": "catalog:", "antd": "catalog:", "mermaid": "catalog:", "tippy.js": "catalog:", "lodash-es": "catalog:", "@repo/editor-common": "workspace:*", "@repo/ui": "workspace:*", "@tiptap/extension-highlight": "catalog:", "@tiptap/extension-mathematics": "catalog:", "@tiptap/extension-blockquote": "catalog:", "@tiptap/extension-bold": "catalog:", "@tiptap/extension-bullet-list": "catalog:", "@tiptap/extension-code": "catalog:", "@tiptap/extension-code-block": "catalog:", "@tiptap/extension-document": "catalog:", "@tiptap/extension-hard-break": "catalog:", "@tiptap/extension-horizontal-rule": "catalog:", "@tiptap/extension-image": "catalog:", "@tiptap/extension-italic": "catalog:", "@tiptap/extension-link": "catalog:", "@tiptap/extension-list-item": "catalog:", "@tiptap/extension-ordered-list": "catalog:", "@tiptap/extension-paragraph": "catalog:", "@tiptap/extension-strike": "catalog:", "@tiptap/extension-table": "catalog:", "@tiptap/extension-table-cell": "catalog:", "@tiptap/extension-table-header": "catalog:", "@tiptap/extension-table-row": "catalog:", "@tiptap/extension-task-item": "catalog:", "@tiptap/extension-task-list": "catalog:", "@tiptap/extension-text": "catalog:", "@tiptap/extension-underline": "catalog:", "@tiptap/extension-collaboration": "catalog:", "@tiptap/extension-dropcursor": "catalog:", "@tiptap/extension-file-handler": "catalog:", "@tiptap/extension-gapcursor": "catalog:", "@tiptap/extension-placeholder": "catalog:", "y-indexeddb": "catalog:", "@repo/common": "workspace:*"}, "devDependencies": {"typescript": "catalog:", "@types/lodash-es": "^4.17.12"}}